from PIL import Image, ImageDraw
import json
import asyncio
from typing import List, Tuple, Union, Optional, Dict, Any
from bson import ObjectId
from io import BytesIO
import requests

from app.models.user import UserTenantDB
from app.v1.api.jobs.models import JobStatus
from app.v1.schema.gemini_model import gemini_model
from google.generativeai import types
from app.v1.processes.gemini_helpers import price_nep

import google.generativeai as genai

from datetime import datetime
from .models import ExtractedData, Diagram, BoundingBox

class ExtractQuestions:
    """
    A class to extract questions and diagrams from images using Gemini AI.
    """
    
    def __init__(self, user_tenant_info: UserTenantDB):
        """
        Initialize the ExtractQuestions class.
        
        Args:
            user_tenant_info: User tenant database information
        """
        self.user_tenant_info = user_tenant_info
        self.model_name = "gemini-2.0-flash"
        # self.safety_settings = [
        #     types.SafetySetting(
        #         category="HARM_CATEGORY_DANGEROUS_CONTENT",
        #         threshold="BLOCK_ONLY_HIGH",
        #     )
        # ]
    
    async def fetch_prompts(self):
        """Fetch system and user prompts from the database."""
        system_instructions = """
You are an expert OCR system that analyzes an image and extracts structured question data.

Your output must follow this format:
- main_text: The main question block (context or description), which may span multiple lines. For math-related content, include LaTeX expressions directly within the text using proper LaTeX syntax (e.g., $x^2 + y^2 = z^2$ for inline math, $$\\frac{a}{b} = c$$ for display math).
- sub_questions: A list of sub-parts (a), (b), etc., each as a separate string. For math-related sub-questions, include LaTeX expressions directly within each sub-question string using proper LaTeX syntax.
- diagrams: An array of bounding boxes for associated diagrams, each formatted as [x1, y1, x2, y2]. This can be null if no diagram is present.

Use the following rules for parsing and classification:

1. A new main_text begins when a line starts with a number followed by a period (e.g., "11.").
   - However, **do NOT treat it as a new main_text** if it is immediately followed by a sub-question marker like "(a)".
   - In such cases (e.g., "19. (a)... or 12. (a)"), treat the whole entry as a sub_question under the previous or implied main_text.
2. Group all lines starting with (a), (b), (c)... as sub_questions under the nearest preceding main_text.
3. Combine multiple lines into one main_text if they appear to be part of the same logical block, even if interrupted by irrelevant phrases.
4. Ignore unrelated footers, headers, names of institutions (e.g., "FB - Tuition Class of..."), or decorative elements.
5. Preserve multi-language sub-questions (e.g., Nepali + English) as single strings within the sub_questions list.
6. Diagrams should be detected based on their bounding boxes. If no diagrams are present, set "diagrams" to null.
7. For math-related questions, **extract all math expressions and equations and represent them using valid LaTeX syntax directly within the main_text and sub_questions fields**. Use $...$ for inline math and $$...$$ for display math.
Your final output must be a valid JSON object containing all_questions, with each question structured as described.

Your goal is to output a JSON object with all_questions as described.
{
  "type": "object",
  "properties": {
    "all_questions": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "main_text": {
            "type": "string",
            "description": "The core question text.",
            "nullable": true
          },
          "sub_questions": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "description": "An array of sub-questions, each with an identifier prefix.",
            "nullable": true
          },
          "diagrams": {
            "type": "array",
            "items": {
              "type": "array",
              "items": {
                "type": "number"
              },
              "minItems": 4,
              "maxItems": 4
            },
            "description": "Bounding boxes for diagrams represented as [x1, y1, x2, y2].",
            "nullable": true
          }
        }
      }
    }
  }
}
"""      
        user_prompt = "Extract the main text, any sub-questions, and up to 3 diagrams from this image. For any math-related content, include LaTeX expressions directly within the main_text and sub_questions fields using proper LaTeX syntax (e.g., $x^2 + y^2 = z^2$ for inline math, $$\\frac{a}{b} = c$$ for display math). Only include clearly graphical diagrams (e.g., labeled figures, flowcharts, shapes). Ignore titles, equations, and any surrounding or decorative text. For each diagram, return its bounding box and a brief description. If multiple diagrams are present, label them by position (e.g., \"Upper diagram\", \"Lower diagram\"). If no diagram is found, omit the diagrams list. Return the output in the specified JSON format."
        return system_instructions, user_prompt

    def calculate_job_usage_metadata_and_pricing(self, total_usage_metadata_n_pricing: dict) -> Dict[str, Any]:
        """Calculate usage metadata and pricing for the job."""
        print("Calculating job usage metadata and pricing...")
        print(total_usage_metadata_n_pricing)

        # Initialize totals
        total_price_nep = 0
        total_prompt_tokens = 0
        total_completion_tokens = 0

        # Process usage metadata
        for usage_metadata in total_usage_metadata_n_pricing.get("usage_metadatas", []):
            # Handle image extraction usage metadata
            if "image_extraction" in usage_metadata:
                extraction_data = usage_metadata["image_extraction"]
                total_prompt_tokens += extraction_data.get("prompt_token_count", 0)
                total_completion_tokens += extraction_data.get("candidates_token_count", 0)
            # Also handle direct usage metadata format
            elif "prompt_token_count" in usage_metadata:
                total_prompt_tokens += usage_metadata.get("prompt_token_count", 0)
                total_completion_tokens += usage_metadata.get("candidates_token_count", 0)

        # Process pricing data
        for pricing in total_usage_metadata_n_pricing.get("pricings", []):
            total_price_nep += pricing.get("total_price_nep", 0)

        return {
            "usage_metadata": {
                "total_prompt_tokens": total_prompt_tokens,
                "total_completion_tokens": total_completion_tokens
            },
            "pricing": {
                "total_price_nep": total_price_nep
            }
        }
    
    def parse_json(self, json_output: str) -> dict:
        """
        Parse JSON output from the model response.
        
        Args:
            json_output: JSON string from model
            
        Returns:
            Parsed JSON as dictionary
        """
        lines = json_output.splitlines()
        for i, line in enumerate(lines):
            if line == "```json":
                json_output = "\n".join(lines[i+1:])  # Remove everything before "```json"
                json_output = json_output.split("```")[0]  # Remove everything after the closing "```"
                break
        
        try:
            return json.loads(json_output)
        except json.JSONDecodeError:
            # If JSON parsing fails, try to clean up the string
            json_output = json_output.strip()
            if json_output.startswith("```") and json_output.endswith("```"):
                json_output = json_output[3:-3].strip()
            return json.loads(json_output)
    
    
    def plot_bounding_boxes(self, image: Image, bounding_boxes_json, padding=100):
        """
        Plots bounding boxes on an image with markers for each diagram using PIL.

        Args:
            image: PIL.Image object.
            bounding_boxes_json: Raw or parsed JSON (string or dict).
            padding: Padding around diagram boxes.

        Returns:
            List of cropped diagram coordinates, the enriched JSON, and list of cropped images.
        """
        width, height = image.size
        # print(f"[DEBUG] Image size: width={width}, height={height}")

        colors = ['red', 'green', 'blue', 'yellow', 'orange', 'pink']

        bounding_boxes_json = self.parse_json(bounding_boxes_json)
        # print("[DEBUG] Parsed bounding_boxes_json")

        all_questions = bounding_boxes_json.get("all_questions", [])
        # print(f"[DEBUG] Total questions found: {len(all_questions)}")

        cropped_diagram_coordinates = []
        cropped_images = []

        for q_idx, question in enumerate(all_questions):
            # print(f"\n[DEBUG] Processing question {q_idx + 1}")
            diagrams = question.get("diagrams")
            if diagrams:
                # print(f"[DEBUG] Found {len(diagrams)} diagram(s)")

                for i, bbox in enumerate(diagrams):
                    # print(f"  [DEBUG] Raw bbox #{i+1}: {bbox}")
                    x1, y1, x2, y2 = bbox

                    abs_x1 = int(x1 / 1000 * width)
                    abs_y1 = int(y1 / 1000 * height)
                    abs_x2 = int(x2 / 1000 * width)
                    abs_y2 = int(y2 / 1000 * height)
                    print(x1, y1, x2, y2)
                    abs_x1, abs_x2 = sorted([abs_x1, abs_x2])
                    abs_y1, abs_y2 = sorted([abs_y1, abs_y2])

                    abs_x1 = max(0, abs_x1 - padding)
                    abs_y1 = max(0, abs_y1 - padding)
                    abs_x2 = min(width, abs_x2 + padding)
                    abs_y2 = min(height, abs_y2 + padding)

                    print(f"  [DEBUG] Absolute padded bbox #{i+1}: ({abs_x1}, {abs_y1}, {abs_x2}, {abs_y2})")

                    cropped_diagram_coordinates.append((abs_x1, abs_y1, abs_x2, abs_y2))
                    cropped = image.crop((abs_x1, abs_y1, abs_x2, abs_y2))
                    cropped_images.append(cropped)
            else:
                print("[DEBUG] No diagrams found for this question")

        return cropped_diagram_coordinates, bounding_boxes_json, cropped_images

    async def process_image(self, media_id: str) -> str:
        """Process an image and update job status."""
        media_collection = self.user_tenant_info.async_db.media
        media = await media_collection.find_one({"_id": ObjectId(media_id)})
        
        if not media or not media.get("object_name"):
            raise ValueError(f"Invalid media or missing object name for media {media_id}")
            
        object_name = media["object_name"]
        job_id = media.get("job_id")
        
        try:
            # Fetch Google API key from config collection
            config = await self.user_tenant_info.async_db.config.find_one({"name": "api-keys"})
            if not config or "GOOGLE_API_KEY" not in config.get("value", {}):
                raise ValueError("Google API key not found in configuration")
            
            # Configure Gemini with API key
            genai.configure(api_key=config["value"]["GOOGLE_API_KEY"])
            
            # Update status to processing
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {"$set": {"status": "processing"}}
            )
            
            # Get the image from MinIO asynchronously
            loop = asyncio.get_event_loop()
            minio_client = self.user_tenant_info.minio_client
            response = await loop.run_in_executor(
                None, 
                minio_client.get_object,
                self.user_tenant_info.minio_bucket_name,
                object_name
            )
            image_data = await loop.run_in_executor(None, response.read)
            image = Image.open(BytesIO(image_data))
            
            # Resize image
            await loop.run_in_executor(None, image.thumbnail, [640, 640], Image.Resampling.LANCZOS)
            
            # Fetch prompts
            system_instructions, user_prompt = await self.fetch_prompts()
            
            # Prepare image for Gemini
            img_byte_arr = BytesIO()
            await loop.run_in_executor(None, image.save, img_byte_arr, 'PNG')
            img_byte_arr.seek(0)  # Reset buffer position
            
            # Generate content with PIL Image
            model = gemini_model(system_instructions=system_instructions, model_name=self.model_name)
            response = await model.generate_content_async([
                user_prompt,
                Image.open(img_byte_arr)  # Pass PIL Image directly
            ])
            
            # Process results
            json_str = response.text

            # Calculate pricing and usage metadata
            # For image processing, use "image" content type with correct pricing
            price = price_nep(response, self.model_name, content_type="image")
            usage_metadata = {
                "prompt_token_count": response.usage_metadata.prompt_token_count,
                "candidates_token_count": response.usage_metadata.candidates_token_count,
            }

            cropped_coords, parsed_json, cropped_images = await loop.run_in_executor(
                None,
                self.plot_bounding_boxes,
                image,
                json_str
            )
            print("cropped_coordinates", cropped_coords)
            print(parsed_json)
            print(cropped_images)
            
            # Save cropped diagrams to MinIO
            for i, cropped_image in enumerate(cropped_images):
                img_byte_arr = BytesIO()
                await loop.run_in_executor(None, cropped_image.save, img_byte_arr, 'PNG')
                img_byte_arr.seek(0)
                
                diagram_object_name = f"{job_id}/output/diagrams/{media_id}_diagram_{i}.png"
                await loop.run_in_executor(
                    None,
                    self.user_tenant_info.minio_client.put_object,
                    self.user_tenant_info.minio_bucket_name,
                    diagram_object_name,
                    img_byte_arr,
                    img_byte_arr.getbuffer().nbytes,
                    'image/png'
                )
                
                # FIXED: Update the correct location in the nested structure
                # Track which diagram we're processing across all questions
                diagram_count = 0
                for question in parsed_json.get('all_questions', []):
                    if question.get('diagrams'):
                        for j in range(len(question['diagrams'])):
                            if diagram_count == i:
                                # Convert the simple array to a dict with coordinates and image_obj_name
                                coords = question['diagrams'][j]
                                question['diagrams'][j] = {
                                    'coordinates': coords,
                                    'image_obj_name': diagram_object_name
                                }
                                break
                            diagram_count += 1
            
            processed_result = await loop.run_in_executor(
                None,
                self.update_json,
                parsed_json,
                cropped_coords
            )
            print(f"Processed result ", processed_result)

            # Create result with usage metadata and pricing
            result_with_metadata = {
                "data": json.loads(processed_result) if isinstance(processed_result, str) else processed_result,
                "usage_metadata": usage_metadata,
                "price_nep": price
            }

            # Update status and result on success
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {
                    "$set": {
                        "status": JobStatus.COMPLETED,
                        "completed_at": datetime.utcnow(),
                        "output_usage_metadata": usage_metadata,
                        "output_pricing": price
                    }
                }
            )

            return result_with_metadata
        except Exception as e:
            print(f"Error processing media {media_id}: {str(e)}")
            # Update status on failure
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": str(e),
                        "completed_at": datetime.utcnow()
                    }
                }
            )
            raise Exception(f"Error processing media {media_id}: {str(e)}")

    async def process_multiple_images(self, media_ids: List[str]) -> List[dict]:
        """Process multiple images in parallel."""
        tasks = [self.process_image(media_id) for media_id in media_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "media_id": media_ids[i],
                    "error": str(result)
                })
            else:
                processed_results.append({
                    "media_id": media_ids[i],
                    "result": json.loads(result) if isinstance(result, str) else result
                })
        
        return processed_results

    async def extract_from_job(self, job_id: str) -> List[dict]:
        """Extract data from all images in a job in parallel."""
        jobs_collection = self.user_tenant_info.async_db.jobs
        job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
        
        if not job or not job.get("items"):
            raise ValueError(f"Invalid job or no items found for job {job_id}")

        # Update job status to processing
        await jobs_collection.update_one(
            {"_id": ObjectId(job_id)},
            {"$set": {"status": JobStatus.INPROGRESS, "updated_at": datetime.utcnow()}}
        )

        # Get media IDs from items
        media_ids = []
        process_items = {}  # Map to track process items by media_id

        # Initialize total usage metadata and pricing
        total_usage_metadata_n_pricing: dict = {
            "usage_metadatas": [], "pricings": []
        }

        for item in job["items"]:
            if item.get("input_id"):
                media_id = str(item["input_id"])
                media_ids.append(media_id)
                process_items[media_id] = item
        
        try:
            # Process all images in parallel
            results = await self.process_multiple_images(media_ids)
            
            media_collection = self.user_tenant_info.async_db.media

            for result in results:
                media_id = result["media_id"]
                process_item = process_items[media_id]

                if "error" in result:
                    # Update process item status to failed
                    process_item["status"] = JobStatus.FAILED
                    process_item["error"] = result["error"]
                else:
                    # Create output media document
                    output_media = {
                        "filename": f"output_{media_id}.json",
                        "content_type": "application/json",
                        "bucket_name": self.user_tenant_info.minio_bucket_name,
                        "object_name": f"{job_id}/output/{media_id}.json",
                        "job_id": ObjectId(job_id),
                        "created_at": datetime.utcnow(),
                        "created_by": job["created_by"],
                        "metadata": {
                            "source_media_id": ObjectId(media_id),
                            "process_type": "extract-image-data",
                            "type": "output"
                        },
                        "output_usage_metadata": result["result"].get("usage_metadata", {}),
                        "output_pricing": {"total_price_nep": result["result"].get("price_nep", 0)}
                    }

                    # Collect usage metadata and pricing for job totals
                    usage_metadata = result["result"].get("usage_metadata", {})
                    if usage_metadata:
                        total_usage_metadata_n_pricing["usage_metadatas"].append({
                            "image_extraction": usage_metadata
                        })

                    pricing = {"total_price_nep": result["result"].get("price_nep", 0)}
                    total_usage_metadata_n_pricing["pricings"].append(pricing)

                    # Insert output media
                    output_result = await media_collection.insert_one(output_media)

                    # Update process item
                    process_item["output_id"] = output_result.inserted_id
                    process_item["status"] = JobStatus.COMPLETED

                    # Upload result to MinIO - use the data field from result
                    result_data = result["result"].get("data", result["result"])
                    json_content = json.dumps(result_data).encode('utf-8')
                    await asyncio.get_event_loop().run_in_executor(
                        None,
                        self.user_tenant_info.minio_client.put_object,
                        self.user_tenant_info.minio_bucket_name,
                        output_media["object_name"],
                        BytesIO(json_content),
                        len(json_content),
                        'application/json'
                    )
            
            # Update job status and items
            all_successful = all(item["status"] == JobStatus.COMPLETED for item in process_items.values())
            status = JobStatus.COMPLETED if all_successful else JobStatus.PARTIALLYCOMPLETED

            usage_metadata_and_pricing = self.calculate_job_usage_metadata_and_pricing(total_usage_metadata_n_pricing)

            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": status,
                        "completed_at": datetime.utcnow(),
                        "error_count": sum(1 for item in process_items.values() if item["status"] == JobStatus.FAILED),
                        "items": list(process_items.values()),
                        "output_usage_metadata": usage_metadata_and_pricing["usage_metadata"],
                        "output_pricing": usage_metadata_and_pricing["pricing"],
                    }
                }
            )
            print("result", results)
            return results
            
        except Exception as e:
            # Update job status on failure
            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": str(e),
                        "completed_at": datetime.utcnow()
                    }
                }
            )
            raise

    def update_json(self, json_data, new_coordinates):
        """
        Updates the bounding boxes in the JSON structure with new coordinates.

        Args:
            json_data: Parsed JSON dict containing 'all_questions'.
            new_coordinates: List of tuples [(x1, y1, x2, y2), ...] for all diagrams across questions.

        Returns:
            Updated JSON with new bounding box coordinates.
        """
        index = 0  # To iterate over new_coordinates
        
        # Make sure we're working with the right structure
        if 'all_questions' not in json_data:
            # If the structure is unexpected, try to fix it
            if not isinstance(json_data, dict):
                json_data = {}
            json_data['all_questions'] = json_data.get('all_questions', [])
        
        for question in json_data['all_questions']:
            if question.get("diagrams"):
                updated_diagrams = []
                for diagram in question['diagrams']:
                    if index >= len(new_coordinates):
                        raise ValueError("Not enough new coordinates to update all diagrams.")
                    
                    # Handle both formats: simple array or dict with coordinates
                    if isinstance(diagram, list) and len(diagram) == 4:
                        # Simple array of coordinates
                        x1, y1, x2, y2 = new_coordinates[index]
                        updated_diagrams.append([x1, y1, x2, y2])
                    elif isinstance(diagram, dict) and 'coordinates' in diagram:
                        # Dict with coordinates and possibly image_obj_name
                        x1, y1, x2, y2 = new_coordinates[index]
                        diagram['coordinates'] = [x1, y1, x2, y2]
                        updated_diagrams.append(diagram)
                    else:
                        # Neither format matches, create a new dict
                        x1, y1, x2, y2 = new_coordinates[index]
                        updated_diagrams.append({
                            'coordinates': [x1, y1, x2, y2]
                        })
                    index += 1
                
                question['diagrams'] = updated_diagrams

        if index != len(new_coordinates):
            raise ValueError(f"Number of coordinates ({len(new_coordinates)}) does not match number of processed diagrams ({index}) in JSON.")

        return json.dumps(json_data, indent=4)